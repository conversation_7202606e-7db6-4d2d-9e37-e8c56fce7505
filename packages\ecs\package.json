{"name": "@nilo/ecs", "version": "0.1.0", "private": true, "main": "./src/index.ts", "module": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint .", "build": "tsc", "typecheck": "tsc --noEmit", "test": "jest"}, "devDependencies": {"@jest/globals": "^30.0.0", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "jest": "^29.7.0", "ts-jest": "^29.3.4", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.11.0", "dependencies": {"ulid": "^3.0.1"}}