import {
  describe,
  it,
  expect,
  jest,
  beforeEach,
  afterEach,
} from "@jest/globals";
import {
  FEATURE_FLAGS_CONFIG,
  isValidFeatureFlag,
  getFeatureFlagDisabledValue,
  getFeatureFlagEnabledValue,
  getFeatureFlagLocalValue,
  isFeatureFlagEnabledByDefault,
  isFeatureFlagValueValid,
  type FeatureFlag,
  type GetFeatureFlagVariantType,
} from "../src/index.js";

describe("Feature Flags Configuration", () => {
  it("should export FEATURE_FLAGS_CONFIG array", () => {
    expect(FEATURE_FLAGS_CONFIG).toBeDefined();
    expect(Array.isArray(FEATURE_FLAGS_CONFIG)).toBe(true);
    expect(FEATURE_FLAGS_CONFIG.length).toBeGreaterThan(0);
  });

  it("should have valid feature flag structure with enabled, on, and off fields", () => {
    FEATURE_FLAGS_CONFIG.forEach((flag) => {
      expect(flag).toHaveProperty("name");
      expect(flag).toHaveProperty("description");
      expect(flag).toHaveProperty("enabled");
      expect(flag).toHaveProperty("on");
      expect(flag).toHaveProperty("off");
      expect(typeof flag.name).toBe("string");
      expect(typeof flag.description).toBe("string");
      expect(typeof flag.enabled).toBe("boolean");
      expect(flag.on).toBeDefined();
      expect(flag.off).toBeDefined();
    });
  });

  it("should validate feature flag names correctly", () => {
    expect(isValidFeatureFlag("testBooleanFlag")).toBe(true);
    expect(isValidFeatureFlag("testStringFlag")).toBe(true);
    expect(isValidFeatureFlag("NonExistentFlag")).toBe(false);
    expect(isValidFeatureFlag("")).toBe(false);
  });

  it("should return correct default (off) values for flags", () => {
    const testBooleanDefault = getFeatureFlagDisabledValue("testBooleanFlag");
    const testStringDefault = getFeatureFlagDisabledValue("testStringFlag");
    const testObjectDefault = getFeatureFlagDisabledValue("testObjectFlag");

    expect(typeof testBooleanDefault).toBe("boolean");
    expect(typeof testStringDefault).toBe("string");
    expect(typeof testObjectDefault).toBe("object");
    expect(testBooleanDefault).toBe(false);
    expect(testStringDefault).toBe("this flag is off");
    expect(testObjectDefault).toEqual({ tier: "free", limit: 10 });
  });

  it("should return correct enabled (on) values for flags", () => {
    const testBooleanEnabled = getFeatureFlagEnabledValue("testBooleanFlag");
    const testStringEnabled = getFeatureFlagEnabledValue("testStringFlag");
    const testObjectEnabled = getFeatureFlagEnabledValue("testObjectFlag");

    expect(typeof testBooleanEnabled).toBe("boolean");
    expect(typeof testStringEnabled).toBe("string");
    expect(typeof testObjectEnabled).toBe("object");
    expect(testBooleanEnabled).toBe(true);
    expect(testStringEnabled).toBe("this flag is on");
    expect(testObjectEnabled).toEqual({ tier: "premium", limit: 100 });
  });

  it("should return correct local values based on enabled state", () => {
    const testBooleanLocal = getFeatureFlagLocalValue("testBooleanFlag");
    const testStringLocal = getFeatureFlagLocalValue("testStringFlag");
    const testObjectLocal = getFeatureFlagLocalValue("testObjectFlag");

    // testStringFlag is enabled by default, so should return 'on' value
    expect(testStringLocal).toBe("this flag is on");

    // testBooleanFlag and testObjectFlag are disabled by default, so should return 'off' values
    expect(testBooleanLocal).toBe(false);
    expect(testObjectLocal).toEqual({ tier: "free", limit: 10 });
  });

  it("should force enabled state when specified", () => {
    const testObjectForceEnabled = getFeatureFlagLocalValue(
      "testObjectFlag",
      true
    );
    const testObjectForceDisabled = getFeatureFlagLocalValue(
      "testObjectFlag",
      false
    );

    expect(testObjectForceEnabled).toEqual({ tier: "premium", limit: 100 });
    expect(testObjectForceDisabled).toEqual({ tier: "free", limit: 10 });
  });

  it("should check enabled state correctly", () => {
    expect(isFeatureFlagEnabledByDefault("testBooleanFlag")).toBe(false);
    expect(isFeatureFlagEnabledByDefault("testStringFlag")).toBe(true);
    expect(isFeatureFlagEnabledByDefault("testObjectFlag")).toBe(false);
  });

  it("should throw error for non-existent flags", () => {
    expect(() => {
      // @ts-expect-error - intentionally testing runtime behavior
      getFeatureFlagDisabledValue("nonExistentFlag");
    }).toThrow('Feature flag "nonExistentFlag" not found in configuration');

    expect(() => {
      // @ts-expect-error - intentionally testing runtime behavior
      getFeatureFlagEnabledValue("nonExistentFlag");
    }).toThrow('Feature flag "nonExistentFlag" not found in configuration');

    expect(() => {
      // @ts-expect-error - intentionally testing runtime behavior
      getFeatureFlagLocalValue("nonExistentFlag");
    }).toThrow('Feature flag "nonExistentFlag" not found in configuration');
  });

  it("should ensure all flags have unique names", () => {
    const flagNames = FEATURE_FLAGS_CONFIG.map((flag) => flag.name);
    const uniqueNames = new Set(flagNames);
    expect(flagNames.length).toBe(uniqueNames.size);
  });
});

describe("Feature Flags Type Safety", () => {
  it("should maintain type consistency between config and types", () => {
    // This test ensures that our type inference is working correctly
    // by checking that the actual values match the expected types

    const flags: FeatureFlag[] = ["testBooleanFlag", "testStringFlag"];

    flags.forEach((flagName) => {
      expect(isValidFeatureFlag(flagName)).toBe(true);
      const defaultValue = getFeatureFlagDisabledValue(flagName);
      const enabledValue = getFeatureFlagEnabledValue(flagName);
      const localValue = getFeatureFlagLocalValue(flagName);
      expect(defaultValue).toBeDefined();
      expect(enabledValue).toBeDefined();
      expect(localValue).toBeDefined();
    });
  });

  it("should provide correct variant types for known flags", () => {
    // Boolean flags
    const testBooleanDefault = getFeatureFlagDisabledValue("testBooleanFlag");
    const testBooleanEnabled = getFeatureFlagEnabledValue("testBooleanFlag");

    expect(typeof testBooleanDefault).toBe("boolean");
    expect(typeof testBooleanEnabled).toBe("boolean");

    // String flag
    const testStringDefault = getFeatureFlagDisabledValue("testStringFlag");
    const testStringEnabled = getFeatureFlagEnabledValue("testStringFlag");
    expect(typeof testStringDefault).toBe("string");
    expect(typeof testStringEnabled).toBe("string");
    expect(testStringDefault).toBe("this flag is off");
    expect(testStringEnabled).toBe("this flag is on");

    // Object flag
    const testObjectDefault = getFeatureFlagDisabledValue("testObjectFlag");
    const testObjectEnabled = getFeatureFlagEnabledValue("testObjectFlag");
    expect(typeof testObjectDefault).toBe("object");
    expect(typeof testObjectEnabled).toBe("object");
    expect(testObjectDefault).toEqual({ tier: "free", limit: 10 });
    expect(testObjectEnabled).toEqual({ tier: "premium", limit: 100 });
  });
});

describe("Feature Flags Integration", () => {
  it("should work with type inference for generic functions", () => {
    function getTypedFeatureFlagDefault<T extends FeatureFlag>(
      flagName: T
    ): GetFeatureFlagVariantType<T> {
      return getFeatureFlagDisabledValue(flagName);
    }

    function getTypedFeatureFlagEnabled<T extends FeatureFlag>(
      flagName: T
    ): GetFeatureFlagVariantType<T> {
      return getFeatureFlagEnabledValue(flagName);
    }

    const typedTestBooleanDefault =
      getTypedFeatureFlagDefault("testBooleanFlag");
    const typedTestBooleanEnabled =
      getTypedFeatureFlagEnabled("testBooleanFlag");

    expect(typeof typedTestBooleanDefault).toBe("boolean");
    expect(typeof typedTestBooleanEnabled).toBe("boolean");
  });

  it("should demonstrate zero-duplication benefits", () => {
    // This test shows that flag names are only defined once in FEATURE_FLAGS_CONFIG
    // and all other types are automatically derived

    const configFlagNames = FEATURE_FLAGS_CONFIG.map((flag) => flag.name);
    const expectedFlags: FeatureFlag[] = [
      "addPlayerControls",
      "rigCharacterModel",
      "testObjectFlag",
      "testStringFlag",
      "testBooleanFlag",
    ];

    expect(configFlagNames.sort()).toEqual(expectedFlags.sort());

    // All flags should be valid
    expectedFlags.forEach((flag) => {
      expect(isValidFeatureFlag(flag)).toBe(true);
    });
  });
});

describe("Feature Flags Validation and Resolution", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock console.warn to avoid cluttering test output
    jest.spyOn(console, "warn").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should validate feature flag values correctly", () => {
    // Boolean flags should accept boolean values
    expect(isFeatureFlagValueValid("testBooleanFlag", true)).toBe(true);
    expect(isFeatureFlagValueValid("testBooleanFlag", false)).toBe(true);
    expect(isFeatureFlagValueValid("testBooleanFlag", "invalid")).toBe(false);

    // String flags should accept string values
    expect(isFeatureFlagValueValid("testStringFlag", "this flag is on")).toBe(
      true
    );
    expect(isFeatureFlagValueValid("testStringFlag", "this flag is off")).toBe(
      true
    );
    expect(isFeatureFlagValueValid("testStringFlag", 123)).toBe(false);

    // Object flags should accept matching object structures
    expect(
      isFeatureFlagValueValid("testObjectFlag", { tier: "free", limit: 10 })
    ).toBe(true);
    expect(
      isFeatureFlagValueValid("testObjectFlag", { tier: "premium", limit: 100 })
    ).toBe(true);
    expect(
      isFeatureFlagValueValid("testObjectFlag", { invalid: "structure" })
    ).toBe(false);
  });
});
