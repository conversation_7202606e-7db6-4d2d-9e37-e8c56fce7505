/**
 * Helper type to validate feature flag structure at compile time
 * @typedef {Object} FeatureFlagConfigItem
 * @property {string} name - The feature flag name
 * @property {string} description - Description of the feature flag
 * @property {boolean} enabled - Whether the flag is enabled by default (for local development)
 * @property {unknown} on - The value served when the flag is enabled
 * @property {unknown} off - The value served when the flag is disabled (default)
 */
type FeatureFlagConfigItem = {
  name: string;
  description: string;
  enabled: boolean;
  on: unknown;
  off: unknown;
};

/**
 * Feature flags configuration array
 * Each flag must have enabled, on, and off values
 * The 'as const satisfies' ensures type safety while maintaining const assertion
 */
export const FEATURE_FLAGS_CONFIG = [
  {
    name: "addPlayerControls",
    description: "Enable player controls option in the toolbar",
    enabled: true,
    on: true,
    off: false,
  },
  {
    name: "rigCharacterModel",
    description:
      "Enable character rigging functionality in toolbar and radial menu",
    enabled: true,
    on: true,
    off: false,
  },
  {
    name: "testObjectFlag",
    description: "Test object flag functionality",
    enabled: false,
    on: { tier: "premium", limit: 100 },
    off: { tier: "free", limit: 10 },
  },
  {
    name: "testStringFlag",
    description: "Test string flag functionality",
    enabled: true,
    on: "this flag is on",
    off: "this flag is off",
  },
  {
    name: "testBooleanFlag",
    description: "Test boolean flag functionality",
    enabled: false,
    on: true,
    off: false,
  },
] as const satisfies readonly FeatureFlagConfigItem[];
