import { expect, test } from "@jest/globals";
import { World } from "../src/world";
import { Component, Name } from "../src/component";
import { EntityId } from "../src/entity";
import {
  Color,
  Position,
  Vector2,
  Velocity,
  SparseMarker,
  OtherMarker,
} from "./common";
import { Query } from "@nilo/ecs";

const Health = new Component<number>("health").withDescription(
  "health of the entity"
);

test("Simple query", () => {
  const world = new World();
  const entity = world.addEntity();
  const entity2 = world.addEntity();
  const entity3 = world.addEntity();

  world.addComponents(entity, [
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 3 }],
    [Name, "entity1"],
  ]);

  world.addComponents(entity2, [
    [Position, { x: 1, y: 4 }],
    [Velocity, { x: 1, y: 3 }],
    [Name, "entity2"],
  ]);

  world.addComponents(entity3, [
    [Position, { x: 8, y: 8 }],
    [Name, "entity3"],
  ]);

  const query = new Query({
    pos: Position,
    name: Name,
  });

  const foundData: {
    entity: EntityId;
    data: { name: string; pos: Vector2 };
  }[] = [];

  query.forEach(world, (entity, data) => {
    foundData.push({ entity: entity.id(), data });
  });

  expect(foundData[0].data).toEqual({
    pos: { x: 1, y: 2 },
    name: "entity1",
  });

  expect(foundData[1].data).toEqual({
    pos: { x: 1, y: 4 },
    name: "entity2",
  });

  expect(foundData[2].data).toEqual({
    pos: { x: 8, y: 8 },
    name: "entity3",
  });

  expect(foundData.length).toBe(3);
});

test("Array pattern query", () => {
  const world = new World();
  const entity = world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Name, "entity1"],
  ]);

  const entity2 = world.addEntity([
    [Position, { x: 1, y: 4 }],
    [Velocity, { x: 1, y: 3 }],
    [Name, "entity2"],
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 8, y: 8 }],
    [Name, "entity3"],
  ]);

  const pattern: [Component<Vector2>, Component<string>] = [Position, Name];
  const query = new Query(pattern);

  const foundData: {
    entity: EntityId;
    data: [Vector2, string];
  }[] = [];

  query.forEach(world, (entity, data) => {
    foundData.push({ entity: entity.id(), data });
  });

  expect(foundData).toEqual([
    { entity: entity, data: [{ x: 1, y: 2 }, "entity1"] },
    { entity: entity3, data: [{ x: 8, y: 8 }, "entity3"] },
    { entity: entity2, data: [{ x: 1, y: 4 }, "entity2"] },
  ]);
});

test("Entity creation during query", () => {
  const world = new World();

  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Name, "entity1"],
  ]);

  const query = new Query({ pos: Position });

  const foundData: { entity: EntityId; data: { pos: Vector2 } }[] = [];

  let entity2: EntityId | null = null;
  query.forEach(world, (entity, data) => {
    // deferred
    entity2 = world.addEntity([
      [Position, { x: 1, y: 2 }],
      [Name, "entity2"],
    ]);

    expect(world.isAlive(entity2)).toBe(true);
    expect(world.hasComponent(entity2, Position)).toBe(false); // NOTE: deferred

    foundData.push({ entity: entity.id(), data });
  });

  expect(entity2).toBeDefined();
  expect(world.isAlive(entity2!)).toBe(true);
  expect(world.hasComponent(entity2!, Position)).toBe(true);

  expect(foundData.length).toBe(1);
});

test("Component addition during query", () => {
  const world = new World();

  const entity = world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Name, "entity1"],
  ]);

  const query = new Query({ pos: Position });
  query.forEach(world, (entity, _data) => {
    world.addComponents(entity.id(), [[Velocity, { x: 1, y: 2 }]]);

    expect(world.hasComponent(entity.id(), Velocity)).toBe(false); // NOTE: deferred until query finishes
  });

  expect(world.hasComponent(entity, Velocity)).toBe(true);
});

test("Changes kept after move", () => {
  const world = new World();

  const entity = world.spawnEntity([[Position, { x: 1, y: 2 }]]);

  entity.addComponent(Name, "entity1");
  const foundData: EntityId[] = [];
  const query = new Query({ pos: Position }).trackModified();

  query.forEach(world, (_entity, _) => {
    foundData.push(_entity.id());
  });

  expect(foundData).toEqual([entity.id()]);
});

test("Granular change tracking", () => {
  const world = new World();

  const entity = world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity1"],
  ]);

  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity2"],
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity3"],
  ]);

  const foundData: string[] = [];
  const query = new Query({ pos: Position, name: Name }).trackModified();

  // First run should include all entities
  expect(extractData()).toEqual(["entity1", "entity2", "entity3"]);

  expect(extractData()).toEqual([]);

  world.setComponent(entity, Position, { x: 1, y: 3 });

  expect(extractData()).toEqual(["entity1"]);

  // Now add a new entity
  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity4"],
  ]);

  expect(extractData()).toEqual(["entity4"]);

  // New archetype
  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Name, "entity5"],
  ]);

  world.setComponent(entity3, Position, { x: 1, y: 3 });

  expect(extractData()).toEqual(["entity3", "entity5"]);

  function extractData(): string[] {
    foundData.length = 0;
    query.forEach(world, (_entity, data) => {
      foundData.push(data.name);
    });

    return foundData;
  }
});

test("Moving entity preserves changes", () => {
  const world = new World();

  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity1"],
  ]);

  const entity2 = world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity2"],
  ]);

  world.addEntity([
    [Position, { x: 1, y: 2 }],
    [Color, { x: 1, y: 2, z: 3 }],
    [Velocity, { x: 1, y: 2 }],
    [Name, "entity3"],
  ]);

  const query = new Query({ pos: Position, name: Name }).trackModified();

  expect(extractData()).toEqual(["entity1", "entity2", "entity3"]);

  expect(extractData()).toEqual([]);

  world.addComponents(entity2, [[Position, { x: 1, y: 3 }]]);

  // Force move to new archetype
  world.addComponents(entity2, [[Color, { x: 1, y: 2, z: 3 }]]);

  expect(extractData()).toEqual(["entity2"]);
  expect(extractData()).toEqual([]);

  world.removeComponent(entity2, Position);

  expect(extractData()).toEqual([]);

  function extractData(): string[] {
    const foundData: string[] = [];
    query.forEach(world, (_entity, data) => {
      foundData.push(data.name);
    });

    return foundData;
  }
});

test("Early return in query", () => {
  const world = new World();

  // Create multiple entities
  world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
  ]);

  world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
  ]);

  world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
  ]);

  const query = new Query({ pos: Position, name: Name });
  const visited: string[] = [];

  query.forEach(world, (_entity, data) => {
    visited.push(data.name);

    if (data.name === "entity2") {
      return true;
    }
  });

  // Should have stopped after entity2, so entity3 should not be visited
  expect(visited.length).toBe(2);
  expect(visited).toEqual(["entity1", "entity2"]);
});

test("Early return in change-tracked query", () => {
  const world = new World();

  // Create multiple entities
  world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
  ]);

  world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
  ]);

  world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
  ]);

  // Modifcation tracking uses different iteration mechanism
  const query = new Query({ pos: Position, name: Name }).trackModified();
  const visited: string[] = [];

  query.forEach(world, (_entity, data) => {
    visited.push(data.name);

    if (data.name === "entity2") {
      return true;
    }
  });

  // Should have stopped after entity2, so entity3 should not be visited
  expect(visited.length).toBe(2);
  expect(visited).toEqual(["entity1", "entity2"]);
});

test("Entity removal during query", () => {
  const world = new World();

  // Create 3 entities with health components
  const entity1 = world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
    [Health, 100],
  ]);

  const entity2 = world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
    [Health, 0], // This entity has 0 health and should be removed
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
    [Health, 50],
  ]);

  const query = new Query({ pos: Position, name: Name, health: Health });

  query.forEach(world, (entity, data) => {
    // Remove entities with 0 health during iteration
    if (data.health === 0) {
      world.removeEntity(entity.id());
    }
  });

  // Assert the 2 remaining entities are alive
  expect(world.isAlive(entity1)).toBe(true);
  expect(world.isAlive(entity3)).toBe(true);

  // Assert the removed entity is dead
  expect(world.isAlive(entity2)).toBe(false);

  const found: number[] = [];
  query.forEach(world, (entity, data) => {
    found.push(data.health);
  });

  expect(found).toEqual([100, 50]);
});

test("Exclude dense component with object pattern", () => {
  const world = new World();

  // Create entities with different component combinations
  const entity1 = world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
  ]);

  const _entity2 = world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
    [Velocity, { x: 1, y: 1 }], // This entity has velocity and should be excluded
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
  ]);

  const _entity4 = world.addEntity([
    [Position, { x: 4, y: 4 }],
    [Name, "entity4"],
    [Velocity, { x: 2, y: 2 }], // This entity has velocity and should be excluded
  ]);

  // Query for entities with Position and Name, but exclude those with Velocity
  const query = new Query({ pos: Position, name: Name }).exclude(Velocity);

  const foundData: EntityId[] = [];

  query.forEach(world, (entity, _) => {
    foundData.push(entity.id());
  });

  // Should only find entity1 and entity3 (entities without Velocity)
  expect(foundData.length).toBe(2);
  expect(foundData.sort()).toEqual([entity1, entity3]);
});

test("Exclude dense component with array pattern", () => {
  const world = new World();

  // Create entities with different component combinations
  const entity1 = world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
  ]);

  const _entity2 = world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
    [Health, 100], // This entity has health and should be excluded
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
  ]);

  // Query for entities with Position and Name using array pattern, but exclude those with Health
  const pattern: [Component<Vector2>, Component<string>] = [Position, Name];
  const query = new Query(pattern).exclude(Health);

  const foundData: EntityId[] = [];

  query.forEach(world, (entity, _) => {
    foundData.push(entity.id());
  });

  // Should only find entity1 and entity3 (entities without Health)
  expect(foundData.length).toBe(2);

  // Verify specific data
  expect(foundData.sort()).toEqual([entity1, entity3]);
});

test("Exclude sparse component with object pattern", () => {
  const world = new World();

  // Create entities with different component combinations
  const entity1 = world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
  ]);

  const entity2 = world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
  ]);

  // Add sparse components to some entities
  world.addComponents(entity2, [[SparseMarker, "marked"]]);
  world.addComponents(entity3, [[SparseMarker, "also_marked"]]);

  // Query for entities with Position and Name, but exclude those with SparseMarker
  const query = new Query({ pos: Position, name: Name }).exclude(SparseMarker);

  const foundData: EntityId[] = [];

  query.forEach(world, (entity, _) => {
    foundData.push(entity.id());
  });

  expect(foundData.sort()).toEqual([entity1]);
});

test("Exclude sparse component with array pattern", () => {
  const world = new World();

  // Create entities with different component combinations
  const entity1 = world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
  ]);

  const entity2 = world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
  ]);

  // Add sparse component to one entity
  world.addComponents(entity2, [[OtherMarker, "special"]]);

  // Query for entities with Position and Name using array pattern, but exclude those with OtherMarker
  const pattern: [Component<Vector2>, Component<string>] = [Position, Name];
  const query = new Query(pattern).exclude(OtherMarker);

  const foundData: EntityId[] = [];

  query.forEach(world, (entity, _) => {
    foundData.push(entity.id());
  });

  // Should find entity1 and entity3 (entities without OtherMarker)
  expect(foundData.sort()).toEqual([entity1, entity3]);
});

test("Multiple exclusions (both dense and sparse)", () => {
  const world = new World();

  // Create entities with different component combinations
  const entity1 = world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
  ]);

  const _entity2 = world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
    [Velocity, { x: 1, y: 1 }], // Has dense component to exclude
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
  ]);

  const _entity4 = world.addEntity([
    [Position, { x: 4, y: 4 }],
    [Name, "entity4"],
    [Health, 100], // Has different dense component to exclude
  ]);

  const entity5 = world.addEntity([
    [Position, { x: 5, y: 5 }],
    [Name, "entity5"],
  ]);

  // Add sparse components to some entities
  world.addComponents(entity3, [[SparseMarker, "marked"]]);
  world.addComponents(entity5, [[OtherMarker, "other"]]);

  // Query with multiple exclusions: exclude Velocity (dense), Health (dense), SparseMarker (sparse), and OtherMarker (sparse)
  const query = new Query({ pos: Position, name: Name })
    .exclude(Velocity)
    .exclude(Health)
    .exclude(SparseMarker)
    .exclude(OtherMarker);

  const foundData: EntityId[] = [];

  query.forEach(world, (entity, _) => {
    foundData.push(entity.id());
  });

  // Should only find entity1 (the only entity without any excluded components)
  expect(foundData.length).toBe(1);
  expect(foundData.sort()).toEqual([entity1]);
});

test("Exclude with change tracking", () => {
  const world = new World();

  // Create entities with different component combinations
  const entity1 = world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
  ]);

  const entity2 = world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
    [Velocity, { x: 1, y: 1 }], // This entity has velocity and should be excluded
  ]);

  const entity3 = world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
  ]);

  // Query with exclusion and change tracking
  const query = new Query({ pos: Position, name: Name })
    .exclude(Velocity)
    .trackModified();

  function extractData(): string[] {
    const foundData: string[] = [];
    query.forEach(world, (_entity, data) => {
      foundData.push(data.name);
    });
    return foundData.sort();
  }

  // First run should include all entities without Velocity (entity1 and entity3)
  expect(extractData()).toEqual(["entity1", "entity3"]);

  // Second run should return empty (no changes)
  expect(extractData()).toEqual([]);

  // Modify entity1 - should appear in next query
  world.setComponent(entity1, Position, { x: 10, y: 10 });
  expect(extractData()).toEqual(["entity1"]);

  // No changes, should be empty
  expect(extractData()).toEqual([]);

  // Add velocity to entity3 (should make it excluded from future queries)
  world.addComponents(entity3, [[Velocity, { x: 5, y: 5 }]]);

  // Modify entity3's position, but it should still be excluded due to having Velocity
  world.setComponent(entity3, Position, { x: 30, y: 30 });
  expect(extractData()).toEqual([]); // entity3 should not appear because it has Velocity now

  // Modify entity1 again
  world.setComponent(entity1, Position, { x: 15, y: 15 });
  expect(extractData()).toEqual(["entity1"]);

  // Remove velocity from entity2 (should make it appear in future change tracking)
  world.removeComponent(entity2, Velocity);

  // Modify entity2's position - should now appear since it no longer has Velocity
  world.setComponent(entity2, Position, { x: 25, y: 25 });
  expect(extractData()).toEqual(["entity2"]);
});

test("Exclude edge cases", () => {
  const world = new World();

  // Create entities
  const entity1 = world.addEntity([
    [Position, { x: 1, y: 1 }],
    [Name, "entity1"],
    [Velocity, { x: 1, y: 1 }],
  ]);

  const entity2 = world.addEntity([
    [Position, { x: 2, y: 2 }],
    [Name, "entity2"],
    [Velocity, { x: 2, y: 2 }],
  ]);

  // Test excluding all entities
  const queryExcludeAll = new Query({ pos: Position, name: Name }).exclude(
    Velocity
  );

  const foundDataExcludeAll: EntityId[] = [];

  queryExcludeAll.forEach(world, (entity, _) => {
    foundDataExcludeAll.push(entity.id());
  });

  expect(foundDataExcludeAll).toEqual([]);

  // Test .first() method with exclusion
  const firstResult = queryExcludeAll.first(world);
  expect(firstResult).toBeNull();

  // Test excluding component that no entity has
  const NonExistentComponent = new Component<string>("non_existent");
  const queryNonExistent = new Query({ pos: Position, name: Name }).exclude(
    NonExistentComponent
  );

  const foundDataNonExistent: EntityId[] = [];

  queryNonExistent.forEach(world, (entity, _) => {
    foundDataNonExistent.push(entity.id());
  });

  // Should find all entities since none have the non-existent component
  expect(foundDataNonExistent.length).toBe(2);
  expect(foundDataNonExistent.sort()).toEqual([entity1, entity2]);

  // Test .first() method with non-existent exclusion
  const firstNonExistent = queryNonExistent.first(world);
  expect(firstNonExistent).not.toBeNull();
  expect(firstNonExistent![1].name).toMatch(/entity[12]/);

  // Create an entity without excluded component to test mixed scenarios
  const entity3 = world.addEntity([
    [Position, { x: 3, y: 3 }],
    [Name, "entity3"],
  ]);

  // Now test query that excludes Velocity - should find entity3 but not entity1 or entity2
  const foundDataMixed: EntityId[] = [];

  queryExcludeAll.forEach(world, (entity, _) => {
    foundDataMixed.push(entity.id());
  });

  expect(foundDataMixed.sort()).toEqual([entity3]);

  // Test .first() method when there is a result
  const firstMixed = queryExcludeAll.first(world);
  expect(firstMixed).not.toBeNull();
  expect(firstMixed![0].id()).toBe(entity3);
  expect(firstMixed![1]).toEqual({ pos: { x: 3, y: 3 }, name: "entity3" });
});
