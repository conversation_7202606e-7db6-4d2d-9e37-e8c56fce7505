{"name": "functions", "scripts": {"build": "tsup", "build:watch": "tsup --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "logs": "firebase functions:log", "test": "jest", "test:coverage": "jest --coverage"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/cerebras": "^0.2.14", "@ai-sdk/fireworks": "^0.2.14", "@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/xai": "^1.2.16", "@fal-ai/client": "^1.5.0", "@firebase/logger": "^0.4.3", "@google-cloud/common": "^6.0.0", "@google-cloud/dns": "^5.1.0", "@google-cloud/functions-framework": "^3.4.2", "@liveblocks/node": "^2.5.2", "@posthog/ai": "^5.2.1", "@nilo/firebase-schema": "file:../../packages/firebase-schema", "@nilo/utilities": "file:../../packages/utilities", "@types/node": "^22.15.3", "ai": "^4.3.16", "firebase-admin": "^12.3.1", "firebase-functions": "^6.3.2", "googleapis": "^159.0.0", "posthog-node": "^5.5.0", "unique-names-generator": "^4.7.1", "ws": "^8.18.0", "zod": "^3.24.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.12", "@types/ws": "^8.5.14", "concurrently": "^8.2.2", "firebase-functions-test": "^3.1.0", "firebase-tools": "^13.15.3", "jest": "^29.7.0", "nodemon": "^3.1.0", "ts-jest": "^29.4.0", "tsup": "^8.5.0", "typescript": "^5.5.4"}, "private": true}