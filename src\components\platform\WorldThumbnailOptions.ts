export type WorldThumbnailOptions = {
  renderOnlineEyecatch: boolean;
  renderOnlineStat: boolean;
  renderViewsEyecatch: boolean;
  renderViewsStat: boolean;
  renderCreatorAboveTitle: boolean;
  renderCreatorByTitle: boolean;
  renderCreatorStat: boolean;
  renderLikesStat: boolean;
  renderSavesStat: boolean;
  renderRemixesStat: boolean;
  thumbnailAspectRatio: string;
  horizontalStatsLayout: boolean;
};

export const worldThumbnailOptionsV1: WorldThumbnailOptions = {
  renderOnlineEyecatch: true,
  renderOnlineStat: false,
  renderViewsEyecatch: true,
  renderViewsStat: true,
  renderCreatorAboveTitle: true,
  renderCreatorByTitle: false,
  renderCreatorStat: false,
  renderLikesStat: false,
  renderSavesStat: true,
  renderRemixesStat: false,
  thumbnailAspectRatio: "aspect-[4/3]",
  horizontalStatsLayout: false,
};

export const worldThumbnailOptionsV2: WorldThumbnailOptions = {
  // Former commented variant 1
  renderOnlineEyecatch: true,
  renderOnlineStat: false,
  renderViewsEyecatch: true,
  renderViewsStat: true,
  renderCreatorAboveTitle: false,
  renderCreatorByTitle: false,
  renderCreatorStat: true,
  renderLikesStat: true,
  renderSavesStat: false,
  renderRemixesStat: true,
  thumbnailAspectRatio: "aspect-[5/4]",
  horizontalStatsLayout: false,
};

export const worldThumbnailOptionsV3: WorldThumbnailOptions = {
  // Former commented variant 2
  renderOnlineEyecatch: true,
  renderOnlineStat: false,
  renderViewsEyecatch: true,
  renderViewsStat: true,
  renderCreatorAboveTitle: true,
  renderCreatorByTitle: false,
  renderCreatorStat: false,
  renderLikesStat: false,
  renderSavesStat: true,
  renderRemixesStat: false,
  thumbnailAspectRatio: "aspect-[16/9]",
  horizontalStatsLayout: true,
};

export const getDefaultWorldThumbnailOptions = (): WorldThumbnailOptions =>
  worldThumbnailOptionsV1;
