import Jolt from "jolt-physics";
import { Vector3, Quaternion } from "three";
import {
  CharacterPhysicsSettings,
  defaultCharacterSettings,
} from "../types/character";
import { JoltInternals } from "../types/jolt";
import { createMulticaster } from "../util/createMulticaster";

// Constants
const LAYER_MOVING = 1;
const LAYER_NON_MOVING = 0;
const LAYER_CHARACTER_SHAPE = 2;
const TIME_MULTIPLIER = 1;

// Utility functions
function degreesToRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

function wrapVec3(vec: Jolt.Vec3 | Jolt.RVec3): Vector3 {
  return new Vector3(vec.GetX(), vec.GetY(), vec.GetZ());
}

// Types
interface CharacterPhysics {
  body: Jolt.CharacterVirtual;
  contactListener: Jolt.CharacterContactListenerJS;
  allowSliding: boolean;
  characterSettings: CharacterPhysicsSettings;
}

export class CharacterControl {
  private character: CharacterPhysics | null = null;
  private joltInternals: JoltInternals;
  private characterSettings: CharacterPhysicsSettings;
  private joltModule: typeof Jolt;
  private joltInterface: Jolt.JoltInterface;
  private physicsSystem: Jolt.PhysicsSystem;
  private lastPosition: Vector3;
  private movementDeltaCappingEnabled: boolean;
  private readonly MAX_POSITION_DELTA = 0.2;
  private readonly MIN_Y_POSITION = -1;
  private readonly MAX_HORIZONTAL_POSITION = 1000;

  constructor(
    characterSettingsArgs: Partial<CharacterPhysicsSettings>,
    joltInternals: JoltInternals
  ) {
    this.joltInternals = joltInternals;
    this.joltModule = joltInternals.joltModule;
    this.joltInterface = joltInternals.joltInterface;
    this.physicsSystem = joltInternals.physicsSystem;
    this.characterSettings = {
      ...defaultCharacterSettings,
      ...characterSettingsArgs,
    } as CharacterPhysicsSettings;
    this.lastPosition = new Vector3();
    this.movementDeltaCappingEnabled = false;

    this.character = this.constructCharacterPhysics();
  }

  private constructCharacterContactListener(): Jolt.CharacterContactListenerJS {
    type Funcs = Jolt.CharacterContactListenerJS;
    return Object.assign(new this.joltModule.CharacterContactListenerJS(), {
      OnAdjustBodyVelocity: createMulticaster<Funcs["OnAdjustBodyVelocity"]>(),
      OnContactValidate: createMulticaster<Funcs["OnContactValidate"]>(),
      OnContactAdded: createMulticaster<Funcs["OnContactAdded"]>(),
      OnContactSolve: createMulticaster<Funcs["OnContactSolve"]>(),
      OnContactRemoved: createMulticaster<Funcs["OnContactRemoved"]>(),
      OnContactPersisted: createMulticaster<Funcs["OnContactPersisted"]>(),
    });
  }

  private createCharacterShape(): Jolt.Shape {
    const characterHeight = this.characterSettings.characterHeight;
    const characterRadius = this.characterSettings.characterRadius;
    const position = new this.joltModule.Vec3(0, 0.5 * characterHeight, 0);
    const rotation = this.joltModule.Quat.prototype.sIdentity();

    return new this.joltModule.RotatedTranslatedShapeSettings(
      position,
      rotation,
      new this.joltModule.CapsuleShapeSettings(
        Math.max(0.5 * characterHeight - characterRadius, 0),
        characterRadius
      )
    )
      .Create()
      .Get();
  }

  private setupContactListener(
    characterContactListener: Jolt.CharacterContactListenerJS
  ): void {
    characterContactListener.OnContactValidate = () => true;
    characterContactListener.OnContactPersisted = () => true;
    characterContactListener.OnContactRemoved = () => true;
    characterContactListener.OnContactSolve = () => true;
  }

  private respawnCharacter(): void {
    if (!this.character) return;

    this.character.body.SetPosition(new this.joltModule.RVec3(0, 5, 0));
    this.character.body.SetLinearVelocity(new this.joltModule.Vec3(0, 0, 0));
    this.character.body.SetRotation(this.joltModule.Quat.prototype.sIdentity());
    this.character.body.CancelVelocityTowardsSteepSlopes(
      this.joltModule.Vec3.prototype.sZero()
    );
  }

  private checkBoundsAndRespawn(): void {
    if (!this.character) return;

    const { x, y, z } = wrapVec3(this.character.body.GetPosition());
    if (y < this.MIN_Y_POSITION) {
      console.warn("Respawned out of bounds character | Y = ", y);
      this.respawnCharacter();
    }
    if (
      x > this.MAX_HORIZONTAL_POSITION ||
      x < -this.MAX_HORIZONTAL_POSITION ||
      z > this.MAX_HORIZONTAL_POSITION ||
      z < -this.MAX_HORIZONTAL_POSITION
    ) {
      console.warn("Respawned out of bounds character | X,Z = ", x, z);
      this.respawnCharacter();
    }
  }

  private capExcessiveMovement(): void {
    if (!this.character || !this.movementDeltaCappingEnabled) return;

    const currentPos = wrapVec3(this.character.body.GetPosition());
    const deltaX = currentPos.x - this.lastPosition.x;
    const deltaZ = currentPos.z - this.lastPosition.z;

    let needsPositionUpdate = false;
    let newX = currentPos.x;
    let newZ = currentPos.z;

    if (Math.abs(deltaX) > this.MAX_POSITION_DELTA) {
      console.warn("🏃 Capping X movement | ΔX = ", deltaX);
      newX = this.lastPosition.x + Math.sign(deltaX) * this.MAX_POSITION_DELTA;
      needsPositionUpdate = true;
    }

    if (Math.abs(deltaZ) > this.MAX_POSITION_DELTA) {
      console.warn("🏃 Capping Z movement | ΔZ = ", deltaZ);
      newZ = this.lastPosition.z + Math.sign(deltaZ) * this.MAX_POSITION_DELTA;
      needsPositionUpdate = true;
    }

    if (needsPositionUpdate) {
      this.character.body.SetPosition(
        new this.joltModule.RVec3(newX, currentPos.y, newZ)
      );
    }

    this.lastPosition.copy(wrapVec3(this.character.body.GetPosition()));
  }

  private updateCharacterPhysics(deltaTime: number): void {
    if (!this.character) return;

    const updateSettings = new this.joltModule.ExtendedUpdateSettings();
    const characterUp = wrapVec3(this.character.body.GetUp());

    // Handle stick to floor
    const enableStickToFloor = true;
    if (!enableStickToFloor) {
      updateSettings.mStickToFloorStepDown =
        this.joltModule.Vec3.prototype.sZero();
    } else {
      const vec = characterUp
        .clone()
        .multiplyScalar(-updateSettings.mStickToFloorStepDown.Length());
      updateSettings.mStickToFloorStepDown.Set(vec.x, vec.y, vec.z);
    }

    // Handle walk stairs
    const enableWalkStairs = true;
    if (!enableWalkStairs) {
      updateSettings.mWalkStairsStepUp = this.joltModule.Vec3.prototype.sZero();
    } else {
      const vec = characterUp
        .clone()
        .multiplyScalar(updateSettings.mWalkStairsStepUp.Length());
      updateSettings.mWalkStairsStepUp.Set(vec.x, vec.y, vec.z);
    }

    const objectVsBroadPhaseLayerFilter =
      this.joltInterface.GetObjectVsBroadPhaseLayerFilter();
    const objectLayerPairFilter = this.joltInterface.GetObjectLayerPairFilter();

    const movingBPFilter = new this.joltModule.DefaultBroadPhaseLayerFilter(
      objectVsBroadPhaseLayerFilter,
      LAYER_MOVING | LAYER_NON_MOVING | LAYER_CHARACTER_SHAPE
    );
    const movingLayerFilter = new this.joltModule.DefaultObjectLayerFilter(
      objectLayerPairFilter,
      LAYER_MOVING | LAYER_NON_MOVING | LAYER_CHARACTER_SHAPE
    );
    const bodyFilter = new this.joltModule.BodyFilter();
    const shapeFilter = new this.joltModule.ShapeFilter();

    this.character.body.ExtendedUpdate(
      deltaTime * TIME_MULTIPLIER,
      this.physicsSystem.GetGravity().MulFloat(1.5),
      updateSettings,
      movingBPFilter,
      movingLayerFilter,
      bodyFilter,
      shapeFilter,
      this.joltInterface.GetTempAllocator()
    );
  }

  public setLinearVelocity(velocity: Vector3): void {
    if (!this.character) return;

    this.character.body.SetLinearVelocity(
      new this.joltModule.Vec3(velocity.x, velocity.y, velocity.z)
    );
  }

  public isSupported(): boolean {
    if (!this.character) return false;
    return this.character.body.IsSupported();
  }

  public getPosition(): Vector3 {
    if (!this.character) return new Vector3();
    return wrapVec3(this.character.body.GetPosition());
  }

  private constructCharacterPhysics(): CharacterPhysics {
    const _characterHeight = this.characterSettings.characterHeight;
    const characterRadius = this.characterSettings.characterRadius;
    const maxSlopeAngle = degreesToRadians(45.0);
    const maxStrength = 1250.0;
    const characterPadding = 0.002;
    const penetrationRecoverySpeed = 0.0;
    const predictiveContactDistance = 0.01;

    // Create character shape
    const characterShape = this.createCharacterShape();

    // Create character settings
    const settings = new this.joltModule.CharacterVirtualSettings();
    settings.mMass = 1000;
    settings.mMaxSlopeAngle = maxSlopeAngle;
    settings.mMaxStrength = maxStrength;
    settings.mShape = characterShape;
    settings.mBackFaceMode = this.joltModule.EBackFaceMode_CollideWithBackFaces;
    settings.mCharacterPadding = characterPadding;
    settings.mPenetrationRecoverySpeed = penetrationRecoverySpeed;
    settings.mPredictiveContactDistance = predictiveContactDistance;
    settings.mSupportingVolume = new this.joltModule.Plane(
      this.joltModule.Vec3.prototype.sAxisY(),
      -characterRadius
    );

    // Set initial position and orientation
    const position = new Vector3();
    position.copy(this.characterSettings.initialPosition);
    const positionJolt = new this.joltModule.RVec3(
      position.x,
      position.y,
      position.z
    );

    const quaternion = new Quaternion();
    quaternion.copy(this.characterSettings.initialOrientation);
    const quaternionJolt = new this.joltModule.Quat(
      quaternion.x,
      quaternion.y,
      quaternion.z,
      quaternion.w
    );

    // Create character
    const character = new this.joltModule.CharacterVirtual(
      settings,
      positionJolt,
      quaternionJolt,
      this.physicsSystem
    );

    // Setup contact listener
    const characterContactListener = this.constructCharacterContactListener();
    character.SetListener(characterContactListener);
    this.setupContactListener(characterContactListener);

    const ctrl = {
      body: character,
      contactListener: characterContactListener,
      allowSliding: false,
      characterSettings: this.characterSettings,
    } as CharacterPhysics;

    return ctrl;
  }

  // Public update method that should be called each frame
  public update(deltaTime: number): void {
    if (!this.character) return;

    // Check bounds and respawn if necessary
    this.checkBoundsAndRespawn();

    // Cap excessive movement if enabled
    this.capExcessiveMovement();

    // Update character physics
    this.updateCharacterPhysics(deltaTime);
  }

  // Getter for the character
  public getCharacter(): CharacterPhysics | null {
    return this.character;
  }

  public getBodyID(): number {
    if (!this.character) return -1;
    return this.character.body.GetID().GetValue();
  }

  // Cleanup method
  public destroy(): void {
    if (this.character) {
      // Clean up physics resources
      this.joltModule.destroy(this.character.contactListener);
      this.joltModule.destroy(this.character.body);
      this.character = null;
    }
  }
}
