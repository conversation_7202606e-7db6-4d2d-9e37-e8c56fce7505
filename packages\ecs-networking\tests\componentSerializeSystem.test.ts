import { describe, test, expect, beforeEach } from "@jest/globals";
import { Struct, f32, u32, text } from "@evenstar/byteform";
import { World, SystemContext } from "@nilo/ecs";
import {
  NetworkComponent,
  ComponentSerializeSystem,
  EntitySerializationState,
  EntitySerializationComponent,
} from "../src/index";

describe("ComponentSerializeSystem", () => {
  let world: World;
  let systemContext: SystemContext;
  let system: ComponentSerializeSystem;

  // Test data structures
  interface Position {
    x: number;
    y: number;
  }

  interface UserInfo {
    id: number;
    name: string;
  }

  // Test schemas
  const positionSchema = new Struct({
    x: f32,
    y: f32,
  });

  const userInfoSchema = new Struct({
    id: u32,
    name: text,
  });

  // Test components
  const PositionComponent = NetworkComponent.SelfData<Position>(
    "position",
    positionSchema
  );
  const UserInfoComponent = NetworkComponent.SelfData<UserInfo>(
    "user_info",
    userInfoSchema
  );

  beforeEach(() => {
    world = new World();
    systemContext = new SystemContext("test-system");
    system = new ComponentSerializeSystem();
    system.init(world, systemContext);
  });

  describe("run method", () => {
    test("serializes component data for entities with the component", () => {
      const entity = world.spawnEntity([
        [PositionComponent, { x: 10.5, y: 20.3 }],
      ]);

      system.run(world, systemContext);

      const entityState = entity.getComponent(EntitySerializationComponent);
      expect(entityState).toBeDefined();
      expect(entityState!.components.has(PositionComponent.KEY)).toBe(true);

      const componentState = entityState!.components.get(PositionComponent.KEY);
      expect(componentState!.dirty).toBe(true);
      expect(componentState!.data).toBeInstanceOf(Uint8Array);
      expect(componentState!.data!.length).toBeGreaterThan(0);
    });

    test("does not process entities without the target component", () => {
      const entity = world.spawnEntity([]); // No components

      system.run(world, systemContext);

      const entityState = entity.getComponent(EntitySerializationComponent);
      expect(entityState).toBeNull();
    });

    test("updates existing EntitySerializationState instead of creating new one", () => {
      const entity = world.spawnEntity([
        [PositionComponent, { x: 1.0, y: 2.0 }],
      ]);

      // First run
      system.run(world, systemContext);
      const firstEntitySerializationState = entity.getComponent(
        EntitySerializationComponent
      );
      expect(firstEntitySerializationState).toBeDefined();

      // Modify component
      entity.setComponent(PositionComponent, { x: 3.0, y: 4.0 });

      // Second run
      system.run(world, systemContext);
      const secondEntitySerializationState = entity.getComponent(
        EntitySerializationComponent
      );

      // Should be the same EntitySerializationState instance, just updated
      expect(secondEntitySerializationState).toBe(
        firstEntitySerializationState
      );

      expect(
        secondEntitySerializationState!.components.get(PositionComponent.KEY)!
          .dirty
      ).toBe(true);
    });

    test("overwrites dirty component data", () => {
      const entity = world.spawnEntity([
        [PositionComponent, { x: 1.0, y: 2.0 }],
      ]);

      // First run
      system.run(world, systemContext);
      const originalData = entity
        .getComponent(EntitySerializationComponent)!
        .components.get(PositionComponent.KEY)!.data;

      // Modify component
      entity.setComponent(PositionComponent, { x: 3.0, y: 4.0 });

      // Second run
      system.run(world, systemContext);
      const newData = entity
        .getComponent(EntitySerializationComponent)!
        .components.get(PositionComponent.KEY)!.data;

      // New component data should be updated
      expect(originalData).not.toEqual(newData);
    });

    test("tracks dirtiness correctly - only updates when component changes", () => {
      const entity = world.spawnEntity([
        [PositionComponent, { x: 1.0, y: 2.0 }],
      ]);

      // First run - should serialize
      system.run(world, systemContext);
      let entityState = entity.getComponent(EntitySerializationComponent)!;
      let componentState = entityState.components.get(PositionComponent.KEY)!;
      expect(componentState.dirty).toBe(true);

      // Reset dirty flag manually to simulate normal operation
      componentState.dirty = false;

      // Second run without changes - should not mark as dirty again
      system.run(world, systemContext);
      entityState = entity.getComponent(EntitySerializationComponent)!;
      componentState = entityState.components.get(PositionComponent.KEY)!;
      expect(componentState.dirty).toBe(false);
    });

    test("processes same component on multiple entities independently", () => {
      const entity1 = world.spawnEntity([
        [PositionComponent, { x: 1.0, y: 2.0 }],
      ]);
      const entity2 = world.spawnEntity([
        [PositionComponent, { x: 3.0, y: 4.0 }],
      ]);

      system.run(world, systemContext);

      // Both entities should have EntitySerializationState
      const entityState1 = entity1.getComponent(EntitySerializationComponent);
      const entityState2 = entity2.getComponent(EntitySerializationComponent);
      expect(entityState1).toBeDefined();
      expect(entityState2).toBeDefined();

      // Both should have the position component serialized
      expect(entityState1!.components.has(PositionComponent.KEY)).toBe(true);
      expect(entityState2!.components.has(PositionComponent.KEY)).toBe(true);

      // Data should be different
      const data1 = entityState1!.components.get(PositionComponent.KEY)!.data!;
      const data2 = entityState2!.components.get(PositionComponent.KEY)!.data!;
      expect(data1).not.toEqual(data2);
    });

    test("processes different components on multiple entities independently", () => {
      const entity1 = world.spawnEntity([
        [PositionComponent, { x: 1.0, y: 2.0 }],
      ]);
      const entity2 = world.spawnEntity([
        [UserInfoComponent, { id: 47, name: "a user" }],
      ]);

      system.run(world, systemContext);

      // Both entities should have EntitySerializationState
      const entityState1 = entity1.getComponent(EntitySerializationComponent);
      const entityState2 = entity2.getComponent(EntitySerializationComponent);
      expect(entityState1).toBeDefined();
      expect(entityState2).toBeDefined();

      // Both should have the position component serialized
      expect(entityState1!.components.has(PositionComponent.KEY)).toBe(true);
      expect(entityState2!.components.has(UserInfoComponent.KEY)).toBe(true);

      // Data should be different
      const data1 = entityState1!.components.get(PositionComponent.KEY)!.data!;
      const data2 = entityState2!.components.get(UserInfoComponent.KEY)!.data!;
      expect(data1).not.toEqual(data2);
    });

    test("handles complex data structures", () => {
      const entity = world.spawnEntity([
        [UserInfoComponent, { id: 123, name: "test-user" }],
      ]);

      system.run(world, systemContext);

      const entityState = entity.getComponent(EntitySerializationComponent);
      expect(entityState).toBeDefined();
      expect(entityState!.components.has(UserInfoComponent.KEY)).toBe(true);

      const componentState = entityState!.components.get(UserInfoComponent.KEY);
      expect(componentState!.dirty).toBe(true);
      expect(componentState!.data).toBeInstanceOf(Uint8Array);
    });

    test("throws error when component has no schema", () => {
      const NoSchemaComponent = new NetworkComponent<Position, Position>(
        "no-schema",
        (data) => data,
        (data) => data
        // No schema provided
      );

      world.spawnEntity([[NoSchemaComponent, { x: 1.0, y: 2.0 }]]);

      expect(() => {
        system.run(world, systemContext);
      }).toThrow("CBOR unimplemented");
    });
  });

  describe("onEntityRemoved method", () => {
    test("removes component from EntitySerializationState when entity is removed", () => {
      const entity = world.spawnEntity([
        [PositionComponent, { x: 1.0, y: 2.0 }],
      ]);

      // First serialize the component
      system.run(world, systemContext);
      let entityState = entity.getComponent(EntitySerializationComponent)!;
      expect(entityState.components.has(PositionComponent.KEY)).toBe(true);

      // Remove the component
      entity.removeComponent(PositionComponent);

      // notify the system
      system.run(world, systemContext);

      // Component should be marked for removal
      entityState = entity.getComponent(EntitySerializationComponent)!;
      const componentState = entityState.components.get(PositionComponent.KEY)!;
      expect(componentState.dirty).toBe(true);
      expect(componentState.data).toBeUndefined();
    });

    test("tracks dirtiness correctly when removing - only updates when necessary", () => {
      const entity = world.spawnEntity([
        [PositionComponent, { x: 1.0, y: 2.0 }],
      ]);

      // Set up a removed state manually
      const entityState = new EntitySerializationState();
      entityState.components.set(PositionComponent.KEY, {
        dirty: false,
        data: undefined,
      });

      // Remove the component from the entity
      entity.removeComponent(PositionComponent);
      entity.addComponent(EntitySerializationComponent, entityState);

      // Try to remove - should not mark as dirty since it's already removed
      system.run(world, systemContext);

      const updatedState = entity.getComponent(EntitySerializationComponent)!;
      const componentState = updatedState.components.get(
        PositionComponent.KEY
      )!;
      expect(componentState.dirty).toBe(false);
    });
  });

  describe("integration scenarios", () => {
    test("complete lifecycle: add component, serialize, remove, serialize", () => {
      const entity = world.spawnEntity([
        [PositionComponent, { x: 1.0, y: 2.0 }],
      ]);

      // Step 1: Initial serialization
      system.run(world, systemContext);
      let entityState = entity.getComponent(EntitySerializationComponent)!;
      let componentState = entityState.components.get(PositionComponent.KEY)!;
      expect(componentState.dirty).toBe(true);
      expect(componentState.data).toBeDefined();

      // Step 2: Simulate processing (reset dirty flag)
      componentState.dirty = false;

      // Step 3: Remove component
      entity.removeComponent(PositionComponent);
      system.run(world, systemContext);
      entityState = entity.getComponent(EntitySerializationComponent)!;
      componentState = entityState.components.get(PositionComponent.KEY)!;
      expect(componentState.dirty).toBe(true);
      expect(componentState.data).toBeUndefined();
    });

    test("multiple systems operating on same entity", () => {
      const entity = world.spawnEntity([
        [PositionComponent, { x: 1.0, y: 2.0 }],
        [UserInfoComponent, { id: 123, name: "test" }],
      ]);

      system.run(world, systemContext);

      const entityState = entity.getComponent(EntitySerializationComponent)!;

      // Both components should be serialized
      expect(entityState.components.has(PositionComponent.KEY)).toBe(true);
      expect(entityState.components.has(UserInfoComponent.KEY)).toBe(true);

      const positionState = entityState.components.get(PositionComponent.KEY)!;
      const userInfoState = entityState.components.get(UserInfoComponent.KEY)!;

      expect(positionState.dirty).toBe(true);
      expect(positionState.data).toBeDefined();
      expect(userInfoState.dirty).toBe(true);
      expect(userInfoState.data).toBeDefined();
    });

    test("system handles entity component changes correctly", () => {
      const entity = world.spawnEntity([
        [PositionComponent, { x: 1.0, y: 2.0 }],
      ]);

      // Initial serialization
      system.run(world, systemContext);
      const firstData = entity
        .getComponent(EntitySerializationComponent)!
        .components.get(PositionComponent.KEY)!.data!;

      // Change component data
      entity.setComponent(PositionComponent, { x: 10.0, y: 20.0 });

      // Serialize again
      system.run(world, systemContext);
      const secondData = entity
        .getComponent(EntitySerializationComponent)!
        .components.get(PositionComponent.KEY)!.data!;

      // Data should be different
      expect(secondData).not.toEqual(firstData);
    });
  });
});
