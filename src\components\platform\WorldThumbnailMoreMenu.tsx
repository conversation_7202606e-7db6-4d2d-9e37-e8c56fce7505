import { useState } from "react";
import { DocumentDuplicateIcon as DocumentDuplicateIconSolid } from "@heroicons/react/24/solid";
import {
  EyeIcon,
  GlobeAltIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import { doc, updateDoc } from "firebase/firestore";
import { useNavigate } from "react-router-dom";
import { bindFunction } from "@/config/firebase";
import { CloneWorldRequest, CreateWorldResponse } from "@nilo/firebase-schema";
import { worldsCollection } from "@/utils/firestoreCollections";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

const cloneWorld = bindFunction<CloneWorldRequest, CreateWorldResponse>(
  "cloneWorld"
);

export const WorldThumbnailMoreMenu = ({
  userId,
  worldId,
  isOwner,
  worldData,
  onNavigate,
  ownerProfile,
}: {
  userId: string;
  worldId: string;
  isOwner: boolean;
  worldData?: {
    isPublic?: boolean;
    ownerId?: string;
  };
  onNavigate?: () => void;
  ownerProfile?: {
    avatarUrl?: string;
    displayName?: string;
    username?: string;
  };
}) => {
  const [isCloning, setIsCloning] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const navigate = useNavigate();

  const handleCloneWorld = async () => {
    if (!userId || !worldId) {
      console.error("🚫 User or World ID is missing for clone action.");
      return;
    }

    try {
      console.debug("🔄 Cloning world:", worldId);
      const result = await cloneWorld({ worldId });
      const newWorldId = (result.data as { worldId?: string })?.worldId;

      if (newWorldId) {
        console.debug(
          "✅ World cloned successfully. New world ID:",
          newWorldId
        );
        // Open the new world in a new tab
        window.open(`/play/${newWorldId}`, "_blank");
      } else {
        console.error("💥 No new world ID received from clone operation");
        throw new Error("Clone operation failed - no new world ID received");
      }
    } catch (error) {
      console.error("💥 Error cloning world:", error);
      throw error;
    }
  };

  const handleVisit = () => {
    if (onNavigate) {
      onNavigate();
    } else {
      navigate(`/play/${worldId}`);
    }
  };

  const handlePublishToggle = async () => {
    if (!worldId || !worldData) return;

    setIsPublishing(true);
    try {
      const newPublishState = !worldData.isPublic;
      console.debug(
        `🚀 ${newPublishState ? "Publishing" : "Unpublishing"} world:`,
        worldId
      );

      await updateDoc(doc(worldsCollection, worldId), {
        isPublic: newPublishState,
      });

      console.debug(
        `✅ World ${newPublishState ? "published" : "unpublished"} successfully`
      );
    } catch (error) {
      console.error("💣 Error toggling publish state:", error);
    } finally {
      setIsPublishing(false);
    }
  };

  const handleMoreByBuilder = () => {
    if (worldData?.ownerId) {
      navigate(`/user/${worldData.ownerId}`);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          className="p-1 rounded-full hover:opacity-80 transition-opacity"
        >
          <img
            src={"/icons/vertical-ellipsis-circle.svg"}
            alt="More options"
            className="w-5 h-5"
          />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="bg-black/90 border-white/20 text-white"
      >
        <DropdownMenuItem
          onClick={async (e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsCloning(true);
            try {
              await handleCloneWorld();
            } catch (error) {
              console.error("💥 Error cloning world:", error);
            } finally {
              setIsCloning(false);
            }
          }}
          className="flex items-center space-x-2 focus:bg-white/10"
          disabled={isCloning}
        >
          <DocumentDuplicateIconSolid className="w-4 h-4" />
          <span>{isCloning ? "Cloning..." : "Remix World"}</span>
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={handleVisit}
          className="flex items-center space-x-2 focus:bg-white/10"
        >
          <EyeIcon className="w-4 h-4" />
          <span>Visit</span>
        </DropdownMenuItem>

        <DropdownMenuSeparator className="bg-white/20" />

        {isOwner && (
          <>
            <DropdownMenuItem
              onClick={handlePublishToggle}
              className="flex items-center space-x-2 focus:bg-white/10"
              disabled={isPublishing}
            >
              <GlobeAltIcon className="w-4 h-4" />
              <span>
                {isPublishing
                  ? worldData?.isPublic
                    ? "Unpublishing..."
                    : "Publishing..."
                  : worldData?.isPublic
                    ? "Unpublish"
                    : "Publish"}
              </span>
            </DropdownMenuItem>
          </>
        )}

        {!isOwner && worldData?.ownerId && (
          <>
            <DropdownMenuItem
              onClick={handleMoreByBuilder}
              className="flex items-center space-x-2 focus:bg-white/10"
            >
              {ownerProfile?.avatarUrl ? (
                <img
                  src={ownerProfile.avatarUrl}
                  alt={
                    ownerProfile.displayName ||
                    ownerProfile.username ||
                    "Builder"
                  }
                  className="w-4 h-4 rounded-full object-cover"
                />
              ) : (
                <UserGroupIcon className="w-4 h-4" />
              )}
              <span>More by this builder</span>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
