# Feature Flags Tests

This directory contains comprehensive tests for the feature flags system, ensuring reliability and type safety.

## Test Structure

### Core Functionality Tests

- **Configuration validation** - Ensures all flags are properly configured with required properties
- **Type inference tests** - Verifies TypeScript types are correctly generated from configuration
- **Resolution tests** - Test that feature flag resolution works correctly with different PostHog values
- **Fallback behavior tests** - Test that the system falls back to default values when PostHog returns unexpected values
- **Local value handling** - Tests for disconnected state behavior
- **Validation logic** - Tests for value validation against expected types

## Test Categories

### 1. Configuration Tests (`FEATURE_FLAGS_CONFIG`)

Tests that verify the feature flags configuration is valid:

```typescript
describe("FEATURE_FLAGS_CONFIG", () => {
  it("should have valid structure");
  it("should have unique flag names");
  it("should have required properties");
});
```

### 2. Utility Function Tests

Tests for all utility functions including:

- `getFeatureFlagLocalValue`
- `getFeatureFlagEnabledValue`
- `getFeatureFlagDisabledValue`
- `isFeatureFlagEnabledByDefault`
- `isValidFeatureFlag`
- `isFeatureFlagValueValid`

### 3. Type Safety Tests

Validates that TypeScript types work correctly:

- Boolean flag responses from PostHog
- Object flag responses with proper structure
- Invalid responses trigger fallbacks
- Type narrowing works as expected

### 4. PostHog Integration Scenarios

Tests various integration scenarios:

- Boolean flag responses from PostHog
- Object flags with structured data
- Invalid responses requiring fallback to local values
- Connection state handling

## Test Data

The tests use the configured feature flags:

- `addPlayerControls` - Boolean flag (enabled: true)
- `rigCharacterModel` - Boolean flag (enabled: true)
- `testObjectFlag` - Object flag (enabled: false)

## Expected Behaviors

### Valid Responses

- PostHog returning `true`/`false` for boolean flags
- PostHog returning objects with compatible structure for object flags
- Proper type inference and validation

### Invalid Responses

- PostHog returning unexpected values requiring fallback
- Network errors or connection issues
- Malformed responses

## Running Tests

```bash
pnpm test

The current test suite focuses on unit testing the configuration and resolution logic. The actual PostHog integration is tested through:

1. **Provider integration** - The React provider handles real PostHog SDK integration
2. **Mock scenarios** - Tests simulate various PostHog response scenarios
3. **Type validation** - Ensures runtime validation works with PostHog responses

## Test Coverage

The tests cover:

- ✅ Configuration validation
- ✅ Type inference accuracy
- ✅ Local value resolution
- ✅ PostHog value integration
- ✅ Fallback behavior
- ✅ Error handling
- ✅ Edge cases

This ensures robust feature flag functionality across all usage scenarios.
