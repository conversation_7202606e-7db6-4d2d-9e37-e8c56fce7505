import { Vector3 } from "three";
import { nanoid } from "nanoid/non-secure";
import { duplicateEntityToData } from "./DuplicateControlsInstantCommit";
import { duplicateControlsInstantBoundingBoxSize } from "./DuplicateControlsInstantBoundingBox";
import { beginTransaction } from "@/core/command/transaction";
import CreateEntityCommand from "@/core/command/commands/createEntity";
import { runCommands } from "@/core/command";
import { ScriptComponent } from "@/core/components";
import { GameEntity } from "@/core/entity";

const POSITION_CHANGE_THRESHOLD = 0.5;
// Percentage of bounding box size to use as threshold
const BOUNDING_BOX_THRESHOLD_RATIO = 0.5;

// Avoiding allocations
const _bboxSize = new Vector3();

export class DuplicateControlsWhileDragging {
  private static lastDuplicationPositions = new Map<string, Vector3>();
  /**
   * Duplicates an entity at its current dragged position while continuing to drag the original
   * Only creates a duplicate if the entity has moved above a threshold since the last duplication
   */
  static commit(entity: GameEntity) {
    const currentPosition = new Vector3();
    entity.getPosition(currentPosition);

    const lastPosition = this.lastDuplicationPositions.get(entity.id);
    if (lastPosition) {
      duplicateControlsInstantBoundingBoxSize(entity, _bboxSize);

      const entitySize = Math.max(_bboxSize.x, _bboxSize.y, _bboxSize.z);

      const dynamicThreshold = Math.max(
        POSITION_CHANGE_THRESHOLD,
        entitySize * BOUNDING_BOX_THRESHOLD_RATIO
      );

      if (currentPosition.distanceTo(lastPosition) < dynamicThreshold) {
        return;
      }
    }

    const entityData = duplicateEntityToData(entity);
    if (!entityData) {
      console.warn("Failed to get entity data for duplication");
      return;
    }

    const transaction = beginTransaction("duplicate-while-dragging");

    const command = new CreateEntityCommand(
      {
        ...entityData,
        id: nanoid(),
      },
      entity.data().getComponent(ScriptComponent)?.toString() ?? null
    );

    runCommands(command);
    transaction.end();

    this.lastDuplicationPositions.set(entity.id, currentPosition.clone());
  }

  static clearAllDuplicationPositions(): void {
    this.lastDuplicationPositions.clear();
  }
}
