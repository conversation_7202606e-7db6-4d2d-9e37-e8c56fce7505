{"name": "@nilo/ecs-networking", "version": "0.1.0", "private": true, "type": "module", "main": "./src/index.ts", "module": "./src/index.ts", "types": "./src/index.ts", "files": ["src"], "scripts": {"build": "tsc", "typecheck": "tsc --noEmit", "test": "jest", "lint": "eslint ."}, "dependencies": {"@evenstar/byteform": "^1.0.1", "@nilo/ecs": "workspace:*"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.12", "ts-jest": "^29.4.0", "typescript": "^5.6.2"}}