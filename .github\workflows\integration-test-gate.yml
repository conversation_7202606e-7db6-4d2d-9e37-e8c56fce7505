name: Integration Test Gate

on:
  pull_request:
    branches: [main]
    types: [opened, synchronize, reopened, ready_for_review]

  # Allow manual triggering (e.g., from PR comments)
  workflow_dispatch:
    inputs:
      pr_number:
        description: "PR number (optional, for tracking)"
        required: false
        type: string
      pr_title:
        description: "PR title (for hotfix bypass detection)"
        required: false
        type: string
      pr_labels:
        description: "PR labels as comma-separated string (for hotfix bypass detection)"
        required: false
        type: string

jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - name: Check for hotfix bypass
        id: bypass-check
        run: |
          # Determine trigger type and set variables accordingly
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            PR_TITLE="${{ inputs.pr_title }}"
            BRANCH_NAME="${{ github.ref_name }}"
            PR_LABELS="${{ inputs.pr_labels }}"
            echo "Triggered via workflow_dispatch"
            echo "PR Title: $PR_TITLE"
            echo "Branch: $BRANCH_NAME"
            echo "Labels: $PR_LABELS"

            # Check if hotfix label exists in comma-separated list
            HAS_HOTFIX_LABEL="false"
            if [[ ",$PR_LABELS," == *",hotfix,"* ]]; then
              HAS_HOTFIX_LABEL="true"
            fi
          else
            PR_TITLE="${{ github.event.pull_request.title }}"
            BRANCH_NAME="${{ github.head_ref }}"
            HAS_HOTFIX_LABEL="${{ contains(github.event.pull_request.labels.*.name, 'hotfix') }}"
            echo "Triggered via pull_request event"
            echo "PR Title: $PR_TITLE"
            echo "Branch: $BRANCH_NAME"
            echo "Has hotfix label: $HAS_HOTFIX_LABEL"
          fi

          BYPASS_REASON=""

          # Sanitize PR title to handle quotes and special characters safely
          PR_TITLE_SANITIZED=$(printf '%q' "$PR_TITLE")
          PR_TITLE_LC="${PR_TITLE_SANITIZED,,}"

          if [[ "$HAS_HOTFIX_LABEL" == "true" ]]; then
            echo "bypass=true" >> $GITHUB_OUTPUT
            BYPASS_REASON="hotfix label detected"
          elif [[ "$PR_TITLE_LC" =~ \[hotfix\] ]]; then
            echo "bypass=true" >> $GITHUB_OUTPUT
            BYPASS_REASON="[hotfix] in PR title detected"
          elif [[ "$BRANCH_NAME" == hotfix/* ]]; then
            echo "bypass=true" >> $GITHUB_OUTPUT
            BYPASS_REASON="hotfix/* branch pattern detected"
          else
            echo "bypass=false" >> $GITHUB_OUTPUT
          fi

          if [[ "$BYPASS_REASON" != "" ]]; then
            echo "🚨 Hotfix bypass: $BYPASS_REASON - bypassing integration test requirement"
            echo "reason=$BYPASS_REASON" >> $GITHUB_OUTPUT
          fi

      - name: Check integration test status
        if: steps.bypass-check.outputs.bypass == 'false'
        run: |
          # Determine commit SHA based on trigger type
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            COMMIT_SHA="${{ github.sha }}"
          else
            COMMIT_SHA="${{ github.event.pull_request.head.sha }}"
          fi

          MAX_WAIT_MINUTES=10
          CHECK_INTERVAL_SECONDS=10

          echo "Checking for integration tests on commit: $COMMIT_SHA"

          # Function to check test status
          check_test_status() {
            # Use local variables to avoid scope conflicts with the main script
            local RUNS_JSON
            local LATEST_RUN
            local RUN_ID
            local FUNC_STATUS
            local CONCLUSION

            # Get all Integration Tests runs for this commit, ordered by created date (newest first)
            RUNS_JSON=$(gh api \
              "/repos/${{ github.repository }}/actions/workflows/integration-tests.yml/runs" \
              --jq ".workflow_runs[] | select(.head_sha == \"$COMMIT_SHA\") | {id, status, conclusion, created_at}" \
              | jq -s 'sort_by(.created_at) | reverse')

            if [ "$RUNS_JSON" = "[]" ]; then
              return 1  # No runs found
            fi

            # Get the most recent run
            LATEST_RUN=$(echo "$RUNS_JSON" | jq -r '.[0]')
            RUN_ID=$(echo "$LATEST_RUN" | jq -r '.id')
            FUNC_STATUS=$(echo "$LATEST_RUN" | jq -r '.status')
            CONCLUSION=$(echo "$LATEST_RUN" | jq -r '.conclusion')

            echo "Latest run: ID=$RUN_ID, Status=$FUNC_STATUS, Conclusion=$CONCLUSION"

            if [ "$FUNC_STATUS" = "completed" ]; then
              if [ "$CONCLUSION" = "success" ]; then
                echo "✅ Integration tests passed on commit $COMMIT_SHA (Run ID: $RUN_ID)"
                return 0  # Success
              else
                echo "❌ Integration tests failed on commit $COMMIT_SHA (Run ID: $RUN_ID)"
                echo "Conclusion: $CONCLUSION"
                echo "View details: https://github.com/${{ github.repository }}/actions/runs/$RUN_ID"
                return 2  # Failed
              fi
            else
              echo "⏳ Integration tests are running on commit $COMMIT_SHA (Run ID: $RUN_ID)"
              echo "🔗 View progress: https://github.com/${{ github.repository }}/actions/runs/$RUN_ID"
              return 3  # Still running
            fi
          }

          # Initial check
          echo "Performing initial check for integration tests..."
          INITIAL_STATUS=0
          check_test_status || INITIAL_STATUS=$?

          if [ $INITIAL_STATUS -eq 0 ]; then
            echo "✅ Initial check: Tests already passed."
            exit 0
          elif [ $INITIAL_STATUS -eq 2 ]; then
            echo "❌ Initial check: Tests already failed."
            exit 1
          elif [ $INITIAL_STATUS -eq 1 ]; then
            # No tests found - provide instructions
            echo "❌ Integration tests have not been run on commit $COMMIT_SHA"
            echo ""
            echo "📋 To run integration tests:"
            echo "Comment '🧪' (or any message containing it) on this PR"
            echo ""
            echo "🚀 Alternative:"
            echo "1. Go to: https://github.com/${{ github.repository }}/actions/workflows/integration-tests.yml"
            echo "2. Click 'Run workflow'"
            
            # Provide branch name based on trigger type
            if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
              echo "3. Select your branch: ${{ github.ref_name }}"
              if [[ "${{ inputs.pr_number }}" != "" ]]; then
                echo "4. Add PR number (optional): ${{ inputs.pr_number }}"
              fi
            else
              echo "3. Select your branch: ${{ github.head_ref }}"
              echo "4. Add PR number (optional): ${{ github.event.pull_request.number }}"
            fi
            
            echo "5. Click 'Run workflow'"
            echo ""
            echo "🚨 Hotfix bypass options:"
            echo "   • Add 'hotfix' label to this PR"
            echo "   • Add '[hotfix]' to PR title"  
            echo "   • Use branch name starting with 'hotfix/'"
            exit 1
          elif [ $INITIAL_STATUS -ne 3 ]; then
            # Handle any other unexpected non-zero exit code from the check function
            echo "⚠️ Unknown or unexpected error during test status check. Exit code: $INITIAL_STATUS"
            exit 1
          fi

          # If we get here, the initial status was 3 (running)
          echo "⏳ Waiting for integration tests to complete (max $MAX_WAIT_MINUTES minutes)..."

          WAIT_COUNT=0
          MAX_WAIT_CYCLES=$((MAX_WAIT_MINUTES * 60 / CHECK_INTERVAL_SECONDS))

          while [ $WAIT_COUNT -lt $MAX_WAIT_CYCLES ]; do
            sleep $CHECK_INTERVAL_SECONDS
            WAIT_COUNT=$((WAIT_COUNT + 1))
            
            ELAPSED_MINUTES=$((WAIT_COUNT * CHECK_INTERVAL_SECONDS / 60))
            echo "Checking status... (${ELAPSED_MINUTES}/${MAX_WAIT_MINUTES} minutes elapsed)"
            
            STATUS=0
            check_test_status || STATUS=$?
            
            if [ $STATUS -eq 0 ]; then
              # Tests passed
              exit 0
            elif [ $STATUS -eq 2 ]; then
              # Tests failed
              exit 1
            fi
            # Status 3 (running) or 1 (somehow disappeared) will continue the loop
          done

          # Timeout reached
          echo "⚠️ Timeout: Integration tests did not complete within $MAX_WAIT_MINUTES minutes"
          echo "The tests may still be running. Check manually:"
          echo "https://github.com/${{ github.repository }}/actions/workflows/integration-tests.yml"
          exit 1
        env:
          GH_TOKEN: ${{ secrets.INTEGRATION_TESTS_TOKEN }}

      - name: Hotfix bypass notification
        if: steps.bypass-check.outputs.bypass == 'true'
        run: |
          echo "🚨 HOTFIX BYPASS ACTIVE"
          echo "Reason: ${{ steps.bypass-check.outputs.reason }}"
          echo "Integration test requirement bypassed for this hotfix."
          echo "Tests can still be run manually if needed."
          echo "Please ensure thorough testing before production deployment."
